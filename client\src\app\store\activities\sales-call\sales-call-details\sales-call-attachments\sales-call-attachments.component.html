<div class="p-3 w-full surface-card border-round shadow-1">
    <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Attachments</h4>

        <div class="flex align-items-center gap-3">
            <p-button label="Add" icon="pi pi-plus-circle" iconPos="right" class="ml-auto"
                [styleClass]="'font-semibold px-3'" [rounded]="true" />

            <p-multiSelect [options]="cols" [(ngModel)]="selectedColumns" optionLabel="header"
                class="table-multiselect-dropdown"
                [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">

        <p-table #dt1 [value]="attachmentdetails" dataKey="id" [rows]="8" [paginator]="true" [loading]="loading"
            responsiveLayout="scroll" [scrollable]="true" class="scrollable-table" [reorderableColumns]="true"
            (onColReorder)="onColumnReorder($event)">

            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn (click)="customSort('name')" class="border-round-left-lg">
                        <div class="flex align-items-center cursor-pointer">
                            File Name
                            <i *ngIf="sortField === 'name'" class="ml-2 pi"
                                [ngClass]="sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'">
                            </i>
                            <i *ngIf="sortField !== 'name'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>
                    <ng-container *ngFor="let col of selectedColumns">
                        <th [pSortableColumn]="col.field" pReorderableColumn (click)="customSort(col.field)">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortField === col.field" class="ml-2 pi"
                                    [ngClass]="sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'">
                                </i>
                                <i *ngIf="sortField !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                    <th class="border-round-right-lg">Action</th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-attachment let-columns="columns">
                <tr class="cursor-pointer">
                    <td pFrozenColumn class="border-round-left-lg">
                        <div class="flex align-items-center gap-2">
                            <i class="material-symbols-rounded text-primary">attach_file</i>
                            <a *ngIf="attachment.link_web_uri" [href]="attachment.link_web_uri" target="_blank"
                               class="text-primary underline">
                                {{ attachment.name || 'Attachment' }}
                            </a>
                            <span *ngIf="!attachment.link_web_uri">
                                {{ attachment.name || 'Attachment' }}
                            </span>
                        </div>
                    </td>
                    <ng-container *ngFor="let col of selectedColumns">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'mime_type'">
                                    {{ attachment.mime_type || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'createdAt'">
                                    {{ attachment.createdAt | date:'short' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'updatedAt'">
                                    {{ attachment.updatedAt | date:'short' }}
                                </ng-container>

                            </ng-container>
                        </td>
                    </ng-container>
                    <td>
                        <button type="button" *ngIf="attachment.link_web_uri"
                            class="p-element p-button p-component p-button-icon-only surface-0 h-2rem w-2rem border-none"
                            (click)="downloadAttachment(attachment)">
                            <i class="material-symbols-rounded text-primary">download</i>
                        </button>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="6" class="border-round-left-lg">No attachments found.</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="6" class="border-round-left-lg">Loading attachments data. Please wait.</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>