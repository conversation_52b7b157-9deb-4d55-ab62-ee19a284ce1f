import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { MessageService } from 'primeng/api';
import { ActivitiesService } from '../../../activities.service';

interface Column {
  field: string;
  header: string;
}

@Component({
  selector: 'app-sales-call-attachments',
  templateUrl: './sales-call-attachments.component.html',
  styleUrl: './sales-call-attachments.component.scss',
})
export class SalesCallAttachmentsComponent implements OnInit, OnDestroy {
  private unsubscribe$ = new Subject<void>();
  public attachmentdetails: any[] = [];
  public bp_id: string = '';
  public activity_id: string = '';
  public loading: boolean = false;

  // Modal properties
  public attachmentDialogVisible: boolean = false;
  public attachmentForm: FormGroup;
  public selectedFile: File | null = null;
  public uploading: boolean = false;
  public attachmentFormSubmitted: boolean = false;

  constructor(
    private activitiesservice: ActivitiesService,
    private formBuilder: FormBuilder,
    private messageService: MessageService
  ) {
    this.attachmentForm = this.formBuilder.group({
      name: ['', Validators.required],
      mime_type: ['']
    });
  }

  private _selectedColumns: Column[] = [];

  public cols: Column[] = [
    { field: 'mime_type', header: 'Type' },
    { field: 'createdAt', header: 'Created On' },
    { field: 'updatedAt', header: 'Updated On' },
  ];

  sortField: string = '';
  sortOrder: number = 1;

  customSort(field: string): void {
    if (this.sortField === field) {
      this.sortOrder = -this.sortOrder;
    } else {
      this.sortField = field;
      this.sortOrder = 1;
    }

    this.attachmentdetails.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = 0;

      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string')
        result = value1.localeCompare(value2);
      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));

      return this.sortOrder * result;
    });
  }

  // Utility to resolve nested fields
  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;
    if (field.indexOf('.') === -1) {
      return data[field];
    } else {
      return field.split('.').reduce((obj, key) => obj?.[key], data);
    }
  }

  ngOnInit() {
    this.activitiesservice.activity
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((response: any) => {
        if (response) {
          this.bp_id = response?.bp_id;
          this.activity_id = response?.activity_id;

          if (this.activity_id) {
            this.loadAttachments();
          }
        }
      });

    this._selectedColumns = this.cols;
  }

  private loadAttachments(): void {
    this.loading = true;
    this.activitiesservice.getCrmAttachments(this.activity_id)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: (response: any) => {
          this.attachmentdetails = response?.data || [];
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading attachments:', error);
          this.attachmentdetails = [];
          this.loading = false;
        }
      });
  }

  downloadAttachment(attachment: any): void {
    if (attachment.link_web_uri) {
      window.open(attachment.link_web_uri, '_blank');
    }
  }

  // Modal methods
  showAddAttachmentDialog(): void {
    this.attachmentDialogVisible = true;
    this.attachmentFormSubmitted = false;
    this.resetAttachmentForm();
  }

  hideAttachmentDialog(): void {
    this.attachmentDialogVisible = false;
    this.resetAttachmentForm();
  }

  private resetAttachmentForm(): void {
    this.attachmentForm.reset();
    this.selectedFile = null;
    this.uploading = false;
    this.attachmentFormSubmitted = false;
  }

  onFileSelect(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;

      // Auto-fill form fields
      this.attachmentForm.patchValue({
        name: file.name,
        mime_type: file.type || 'application/octet-stream'
      });
    }
  }

  removeSelectedFile(): void {
    this.selectedFile = null;
    this.attachmentForm.patchValue({
      name: '',
      mime_type: ''
    });

    // Reset file input
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  onSubmitAttachment(): void {
    this.attachmentFormSubmitted = true;

    if (this.attachmentForm.invalid || !this.selectedFile) {
      return;
    }

    this.uploading = true;

    const attachmentData = {
      name: this.attachmentForm.get('name')?.value,
      mime_type: this.attachmentForm.get('mime_type')?.value,
      activity_id: this.activity_id,
      link_web_uri: '', // This would be set after file upload
    };

    this.createAttachment(attachmentData);
  }

  private createAttachment(attachmentData: any): void {
    this.activitiesservice.createCrmAttachment(attachmentData)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: (response: any) => {
          this.uploading = false;
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Attachment created successfully'
          });

          this.hideAttachmentDialog();
          this.loadAttachments(); // Refresh the list
        },
        error: (error: any) => {
          this.uploading = false;
          console.error('Error creating attachment:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to create attachment. Please try again.'
          });
        }
      });
  }

  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }

  onColumnReorder(event: any) {
    const draggedCol = this._selectedColumns[event.dragIndex];
    this._selectedColumns.splice(event.dragIndex, 1);
    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);
  }


  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
